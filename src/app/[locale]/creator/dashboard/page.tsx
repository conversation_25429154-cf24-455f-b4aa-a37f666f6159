'use client';

import React from 'react';
import { useState, useEffect, useCallback } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useRouter } from '@/lib/i18n';
import { useParams } from 'next/navigation';
import { WalletButton } from '@/components/wallet/WalletButton';
import { useTranslations } from 'next-intl';
import { CreatorOnboarding } from '@/components/creator/CreatorOnboarding';
import { LayoutDashboard, Clapperboard, BarChart3, Gift, Settings, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link as I18nLink } from '@/lib/i18n';
import Image from 'next/image';
import { DashboardOverview } from '@/components/creator/DashboardOverview';
import { DashboardContent } from '@/components/creator/DashboardContent';
import { DashboardTips } from '@/components/creator/DashboardTips';
import { DashboardAnalytics } from '@/components/creator/DashboardAnalytics';
import { DashboardSettings } from '@/components/creator/DashboardSettings';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

// Data type definitions (can be expanded based on API responses)
interface Profile {
  id: string;
  wallet_address: string;
  username?: string;
  avatar_url?: string;
  is_creator: boolean;
  created_at: string;
}

interface Stream {
  id: string;
  title: string;
  status: string;
  views?: number;
  created_at: string;
  playback_url?: string;
  thumbnail_url?: string;
  is_active?: boolean;
  is_actually_live?: boolean;
}

// Use the more comprehensive Tip definition
interface Tip {
  id: string;
  amount: number; // Total amount of the tip
  platform_fee_amount?: number;
  creator_amount?: number; // Amount received by creator after fee
  tx_signature: string;
  created_at: string;
  tipper_id?: string; 
  tipper_username?: string; 
  stream_title?: string; 
}

interface NFTMint {
  id: string;
  nft_name: string; // Or other relevant NFT details
  created_at: string;
  buyer_username?: string; // Or owner username
  tx_signature: string; // Mint transaction signature
  metadata_uri?: string;
}

interface EarningsData {
  total_bonk: number; // Total BONK earned by creator (net)
  tip_count: number;
  recent_tips: Tip[]; // Should now use the comprehensive Tip interface
  // Potentially add total_nfts_value or similar if applicable
}

interface DashboardData {
  profile: Profile | null;
  earnings: EarningsData | null;
  streams: Stream[];
  nfts: NFTMint[]; // Data for NFTs minted from creator's streams
  activeStream: Stream | null; 
}

const initialDashboardData: DashboardData = {
  profile: null,
  earnings: null,
  streams: [],
  nfts: [],
  activeStream: null,
};

export default function CreatorDashboardPage() {
  const params = useParams();
  const locale = params.locale as string || 'en';
  const t = useTranslations('creator.dashboard');
  const commonT = useTranslations('common');
  const { connected, publicKey, signMessage } = useWallet();
  const router = useRouter();

  const [dashboardData, setDashboardData] = useState<DashboardData>(initialDashboardData);
  const [isLoading, setIsLoading] = useState(true);
  const [activeView, setActiveView] = useState('overview'); // overview, content, analytics, tips, settings

  // Authentication state (using same pattern as collection page)
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authLoading, setAuthLoading] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  // Authentication function (same as collection page)
  const authenticate = useCallback(async () => {
    if (!connected || !publicKey || !signMessage) {
      setAuthError('Wallet not connected');
      return false;
    }

    setAuthLoading(true);
    setAuthError(null);

    try {
      const walletAddress = publicKey.toString();
      const message = `bonkstream authentication ${walletAddress} ${Date.now()}`;
      
      const signature = await signMessage(new TextEncoder().encode(message));
      const signatureBase64 = Buffer.from(signature).toString('base64');
      
      // Store authentication data
      const authData = {
        signature: signatureBase64,
        message,
        walletAddress,
        expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
      };
      
      localStorage.setItem('walletAuthSignature', authData.signature);
      localStorage.setItem('walletAuthMessage', authData.message);
      localStorage.setItem('walletAddress', authData.walletAddress);
      localStorage.setItem('walletAuthExpiry', authData.expiresAt.toString());
      
      setIsAuthenticated(true);
      setAuthLoading(false);
      return true;
    } catch (error) {
      console.error('Authentication failed:', error);
      setAuthError('Failed to authenticate wallet');
      setAuthLoading(false);
      return false;
    }
  }, [connected, publicKey, signMessage]);

  // Check for stored authentication
  const checkStoredAuth = useCallback(() => {
    if (!connected || !publicKey) return false;
    
    const signature = localStorage.getItem('walletAuthSignature');
    const message = localStorage.getItem('walletAuthMessage');
    const address = localStorage.getItem('walletAddress');
    const expiry = localStorage.getItem('walletAuthExpiry');
    
    if (!signature || !message || !address || !expiry) return false;
    if (address !== publicKey.toString()) return false;
    if (Date.now() > parseInt(expiry)) return false;
    
    return true;
  }, [connected, publicKey]);

  // Create authentication headers
  const createAuthHeaders = (walletAddress: string) => {
    const signature = localStorage.getItem('walletAuthSignature');
    const message = localStorage.getItem('walletAuthMessage');
    
    if (!signature || !message) {
      throw new Error('No valid authentication found');
    }
    
    return {
      'Authorization': `Wallet ${walletAddress}`,
      'x-wallet-signature': signature,
      'x-wallet-message': message
    };
  };

  // Trigger authentication when wallet connects
  useEffect(() => {
    if (connected && publicKey && !isAuthenticated && !authLoading) {
      if (checkStoredAuth()) {
        setIsAuthenticated(true);
      } else {
        authenticate();
      }
    }
  }, [connected, publicKey, isAuthenticated, authLoading, authenticate, checkStoredAuth]);

  // Check authentication and load data
  useEffect(() => {
    const loadCreatorData = async (walletAddress: string) => {
      setIsLoading(true);
      try {
        let userProfile = dashboardData.profile;

        // Fetch profile if not already fetched or if wallet changed
        if (!userProfile || userProfile.wallet_address !== walletAddress) {
          userProfile = await fetchProfile(walletAddress);
        }

        if (userProfile) {
          // Always load the profile, and try to fetch creator data
          let creatorData = { 
            earnings: { total_bonk: 0, tip_count: 0, recent_tips: [] } as EarningsData, 
            streams: [], 
            nfts: [], 
            activeStream: null 
          };
          
          try {
            creatorData = await fetchCreatorData(userProfile.wallet_address);
          } catch (error) {
            console.warn('Could not load creator data, using defaults:', error);
            // Keep the default structure already initialized above
          }
          
          setDashboardData({
            profile: userProfile,
            ...creatorData,
          });
        } else {
          // No profile found - show onboarding
          setDashboardData(prev => ({ ...prev, profile: null }));
        }
      } catch (err) {
        console.error('Dashboard loading error:', err);
        if ((err as Error).message !== 'Redirecting to home page - profile does not exist.'){
          console.error((err as Error).message || t('errors.loadDashboardFailed'));
        }
      } finally {
        setIsLoading(false);
      }
    };

    const loadDashboard = async () => {
      setIsLoading(true);

      // If wallet is not connected, redirect to home
      if (!connected || !publicKey) {
        setIsLoading(false);
        router.push(`/`);
        return;
      }

      // Wait for authentication to complete
      if (!isAuthenticated) {
        setIsLoading(false);
        return;
      }

      const walletAddress = publicKey.toString();
      
      try {
        await loadCreatorData(walletAddress);
      } catch (err) {
        console.error('Dashboard: Error loading data', err);
        setIsLoading(false);
      }
    };
    
    loadDashboard();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [connected, publicKey, isAuthenticated, locale, router, dashboardData.profile, t]); // fetchCreatorData and fetchProfile intentionally omitted to prevent infinite loops

  const fetchProfile = async (walletAddress: string) => {
    const headers = {
      'Content-Type': 'application/json',
      ...createAuthHeaders(walletAddress)
    };

    const response = await fetch(`/${locale}/api/profile?wallet=${walletAddress}`, { 
      cache: 'no-cache',
      headers 
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Authentication required');
      }
      throw new Error(t('errors.loadProfileFailed'));
    }
    
    const data = await response.json();
    
    // Return null if no profile exists - this is normal for new users who need onboarding
    if (!data.profile) {
      return null;
    }
    return data.profile;
  };

  const fetchCreatorData = async (walletAddress: string) => {
    try {
      const headers = {
        'Content-Type': 'application/json',
        ...createAuthHeaders(walletAddress)
      };

      // Use the comprehensive dashboard API with wallet address
      const dashboardRes = await fetch(`/${locale}/api/creator/dashboard?timeRange=all&wallet=${walletAddress}`, { 
        cache: 'no-cache',
        credentials: 'include',
        headers
      });
        
      if (!dashboardRes.ok) {
        console.warn('Failed to load dashboard data:', dashboardRes.status, dashboardRes.statusText);
        throw new Error('Failed to load dashboard data');
      }

      const dashboardData = await dashboardRes.json();
      
      // Find active stream - a stream that is actually live (not just marked as live in DB)
      const activeStream = dashboardData.streams?.find((s: Stream) => s.is_actually_live) || null;

      return {
        earnings: {
          total_bonk: dashboardData.earnings?.creatorAmount || 0,
          tip_count: dashboardData.earnings?.tipCount || 0,
          recent_tips: dashboardData.tips?.slice(0, 10) || []
        }, 
        streams: dashboardData.streams || [],
        nfts: dashboardData.nfts || [],
        activeStream: activeStream,
      };
    } catch (err) {
      console.error('Error fetching creator data:', err);
      console.error(t('errors.loadCreatorDataFailed'));
      return { 
        earnings: { total_bonk: 0, tip_count: 0, recent_tips: [] }, 
        streams: [], 
        nfts: [], 
        activeStream: null 
      };
    }
  };

  const handleOnboardingComplete = (updatedProfile: Profile) => {
    setDashboardData(prev => ({ ...prev, profile: updatedProfile }));
    // Fetch creator data after onboarding completion
    fetchCreatorData(updatedProfile.wallet_address).then(creatorData => {
      setDashboardData(prev => ({ ...prev, ...creatorData }));
    });
    setActiveView('overview'); // Switch to overview after onboarding
  };

  const handleStreamDeleted = (streamId: string) => {
    // Remove the deleted stream from the local state
    setDashboardData(prev => ({
      ...prev,
      streams: prev.streams.filter(stream => stream.id !== streamId)
    }));
  };
  
  const getCreatorPageUrl = () => {
    if (dashboardData.profile?.username) {
      // Ensure the base URL is correct for your deployment
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
      return `${baseUrl}/${locale}/c/${dashboardData.profile.username}`;
    }
    return null;
  };

  // Render states: Not connected, Loading, Error
  if (!connected) {
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
        <main className="flex-1 flex items-center justify-center p-4">
          <div className="text-center max-w-md mx-auto">
            <h1 className="bonk-header text-2xl mb-4">{t('connectWallet.title')}</h1>
            <p className="bonk-body text-white/60 mb-6">{t('connectWallet.description')}</p>
            <WalletButton className="bonk-btn bg-bonk-orange text-white px-6 py-3 rounded-md hover:bg-bonk-orange/80 transition" />
          </div>
        </main>
      </div>
    );
  }

  // Authentication loading state
  if (authLoading || (!isAuthenticated && connected)) {
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
        <main className="flex-1 flex items-center justify-center p-4">
          <div className="flex flex-col items-center text-center">
            <LoadingSpinner size="lg" text="" />
            <h2 className="bonk-header text-xl mb-2 mt-4">Authenticating...</h2>
            <p className="bonk-body text-white/60 mb-6 max-w-md">
              Please sign the message in your wallet to access your dashboard.
            </p>
          </div>
        </main>
      </div>
    );
  }

  // Authentication error state
  if (authError) {
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
        <main className="flex-1 flex items-center justify-center p-4">
          <div className="text-center max-w-md mx-auto p-6 bg-bonk-widget-dark rounded-lg border border-bonk-red/30">
            <h2 className="bonk-header text-xl mb-2">Authentication Failed</h2>
            <p className="bonk-body text-white/60 mb-6">
              {authError}
            </p>
            <Button onClick={() => authenticate()} className="bonk-btn bg-bonk-orange hover:bg-bonk-orange/80 text-white mr-2">
              Try Again
            </Button>
            <Button onClick={() => window.location.reload()} className="bonk-btn bg-bonk-widget-black hover:bg-bonk-widget-dark text-white">
              Refresh Page
            </Button>
          </div>
        </main>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
        <main className="flex-1 flex items-center justify-center p-4">
          <LoadingSpinner size="lg" text={t('loading')} />
        </main>
      </div>
    );
  }

  // Show onboarding for new users who haven't set up their profile yet
  if (!dashboardData.profile || (dashboardData.profile && !dashboardData.profile.username)) {
    // Create a temporary profile for users who don't have one yet
    const tempProfile = dashboardData.profile || {
      id: 'temp',
      wallet_address: publicKey?.toString() || '',
      is_creator: false,
      created_at: new Date().toISOString()
    };
    
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col items-center justify-center p-4">
        <CreatorOnboarding 
          profile={tempProfile} 
          locale={locale} 
          onOnboardingComplete={handleOnboardingComplete} 
        />
      </div>
    );
  }
  
  // Main Dashboard View for all authenticated users
  if (dashboardData.profile) {
    const creatorPageUrl = getCreatorPageUrl();

    const navigationItems = [
      {id: 'overview', label: t('nav.overview'), icon: LayoutDashboard },
      {id: 'content', label: t('nav.content'), icon: Clapperboard },
      {id: 'analytics', label: t('nav.analytics'), icon: BarChart3, comingSoon: true },
      {id: 'tips', label: t('nav.tips'), icon: Gift },
      {id: 'settings', label: t('nav.settings'), icon: Settings },
    ];

    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col md:flex-row">
        {/* Desktop Sidebar Navigation - Hidden on Mobile */}
        <nav className="hidden md:flex w-64 bonk-widget p-4 space-y-2 border-r border-bonk-orange/20 flex-col">
          <div className="mb-6">
            <I18nLink href="/" className="flex items-center space-x-2 mb-2">
              <Image src="/logo.png" alt="bonkstream logo" width={32} height={32} className="h-8 w-auto"/> 
              <span className="bonk-header text-xl text-white">bonkstream</span>
            </I18nLink>
            <p className="bonk-body text-xs text-white/60">Creator Dashboard</p>
          </div>

          {navigationItems.map(item => (
            <Button
              key={item.id}
              variant={activeView === item.id ? 'secondary' : 'ghost'}
              className={`bonk-btn w-full justify-start text-sm ${activeView === item.id ? 'bg-bonk-orange-red-2 text-white' : 'hover:bg-bonk-widget-black/50'}`}
              onClick={() => setActiveView(item.id)}
            >
              <item.icon className="mr-2 h-4 w-4" />
              <span className="flex-1 text-left">{item.label}</span>
              {item.comingSoon && (
                <span className="text-[8px] bg-yellow-600 text-black px-1 py-0.5 rounded-full ml-2 font-medium">
                  Soon
                </span>
              )}
            </Button>
          ))}
          
          <div className="mt-auto pt-4 border-t border-bonk-orange/20">
            {creatorPageUrl && (
                <a 
                    href={creatorPageUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="bonk-body text-xs text-bonk-orange hover:text-bonk-yellow flex items-center mb-2"
                >
                    <ExternalLink className="w-3 h-3 mr-1" /> 
                    {t('nav.viewPublicPage')}
                </a>
            )}

          </div>
        </nav>

        {/* Mobile Navigation - Horizontal Scrollable Tab Bar */}
        <div className="md:hidden bonk-widget border-b border-bonk-orange/20">
          <div className="px-4 py-2">
            <I18nLink href="/" className="flex items-center space-x-2 mb-3">
              <Image src="/logo.png" alt="bonkstream logo" width={24} height={24} className="h-6 w-auto"/>
              <span className="bonk-header text-lg text-white">bonkstream</span>
            </I18nLink>
          </div>
          <div className="overflow-x-auto scrollbar-hide">
            <div className="flex space-x-1 px-4 pb-3 min-w-max">
              {navigationItems.map(item => (
                <Button
                  key={item.id}
                  variant={activeView === item.id ? 'secondary' : 'ghost'}
                  size="sm"
                  className={`bonk-btn flex-shrink-0 text-xs ${activeView === item.id ? 'bg-bonk-orange-red-2 text-white' : 'hover:bg-bonk-widget-black/50'}`}
                  onClick={() => setActiveView(item.id)}
                >
                  <item.icon className="mr-1 h-3 w-3" />
                  <span>{item.label}</span>
                  {item.comingSoon && (
                    <span className="ml-1 text-[8px] bg-yellow-600 text-black px-1 py-0.5 rounded font-medium">
                      Soon
                    </span>
                  )}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <main className="flex-1 p-4 md:p-10 overflow-y-auto">
          <header className="mb-6 md:mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h1 className="bonk-header text-2xl md:text-3xl">
              {activeView === 'overview' && t('nav.overview')}
              {activeView === 'content' && t('nav.content')}
              {activeView === 'analytics' && t('nav.analytics')}
              {activeView === 'tips' && t('nav.tips')}
              {activeView === 'settings' && t('nav.settings')}
            </h1>
            {/* Add WalletButton or other header items if needed */}
            <div className="flex items-center space-x-2">
                {dashboardData.profile.avatar_url ? (
                    <Image src={dashboardData.profile.avatar_url} alt="Avatar" width={32} height={32} className="w-8 h-8 rounded-full" />
                ) : (
                    <div className="w-8 h-8 rounded-full bg-bonk-widget-black flex items-center justify-center text-sm bonk-body"> 
                        {dashboardData.profile.username?.charAt(0).toUpperCase() || 'U'}
        </div>
                )}
                <span className="text-sm">{dashboardData.profile.username || 'Creator'}</span>
      </div>
          </header>

          {/* Placeholder for content based on activeView */}
          <div className="bg-gray-900 p-4 md:p-6 rounded-lg shadow-lg min-h-[calc(100vh-200px)] md:min-h-[calc(100vh-150px)]">
            {activeView === 'overview' && (
              <DashboardOverview 
                profile={dashboardData.profile}
                earnings={dashboardData.earnings}
                streams={dashboardData.streams}
                nfts={dashboardData.nfts}
                activeStream={dashboardData.activeStream}
                locale={locale}
              />
            )}
            {activeView === 'content' && (
              <DashboardContent 
                streams={dashboardData.streams}
                locale={locale}
                onStartNewStream={() => alert(t('content.startStreamPlaceholder'))} // Placeholder action
                onStreamDeleted={handleStreamDeleted}
              />
            )}
            {activeView === 'analytics' && (
              <DashboardAnalytics 
                analyticsData={null} // Pass null for now, component handles placeholder
                locale={locale} 
              />
            )}
            {activeView === 'tips' && (
              <DashboardTips 
                tips={dashboardData.earnings?.recent_tips || []} 
                locale={locale} 
              />
            )}
            {activeView === 'settings' && (
              <DashboardSettings 
                profile={dashboardData.profile}
                locale={locale}
                onProfileUpdate={(updatedProfile) => {
                  setDashboardData(prev => ({ ...prev, profile: updatedProfile }));
                }}
              />
            )}
          </div>
        </main>
      </div>
    );
  }

  // Fallback if profile somehow not loaded, though earlier checks should catch this.
  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center">
       <p>{commonT('unexpectedError')}</p>
    </div>
  )
} 